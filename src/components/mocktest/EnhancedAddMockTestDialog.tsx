import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Calendar as CalendarIcon,
  Target,
  BookOpen,
  Settings,
  ChevronLeft,
  ChevronRight,
  Check,
  Trash2,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { saveMockTest, calculateTestTotals } from '@/utils/mockTestUtils';
import { 
  MockTest, 
  TestType, 
  TestEnvironment, 
  DifficultyLevel, 
  ConfidenceLevel,
  SubjectMarks 
} from '@/types/mockTest';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { useSupabaseSubjectStore } from '@/stores/supabaseSubjectStore';
import { categoryStorage } from '@/utils/mockTestLocalStorage';
import { TestCategory } from '@/types/mockTest';

interface EnhancedAddMockTestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTestAdded?: () => void;
  initialData?: Partial<MockTest>;
}

interface SubjectMarkInput {
  subjectId: string;
  marksObtained: string;
  totalMarks: string;
  timeSpent?: string;
  questionsAttempted?: string;
}

interface FormData {
  // Basic Information
  name: string;
  date: Date;
  time: string;
  categoryId: string;
  testType: TestType;
  
  // Test Configuration
  testEnvironment: TestEnvironment;
  duration: string;
  totalQuestions: string;
  testPaperUrl: string;
  
  // Goals and Preparation
  targetScore: string;
  confidenceLevel: ConfidenceLevel;
  preparationTime: string;
  studyMaterials: string[];
  expectedDifficulty: DifficultyLevel;
  
  // Subject Details
  subjectInputs: SubjectMarkInput[];
  
  // Analysis Setup
  enableMistakeTracking: boolean;
  enableTakeawayCollection: boolean;
  reviewReminderEnabled: boolean;

  // Initial Analysis Data
  syllabus: string[];
  initialMistakes: string[];
  initialTakeaways: string[];

  // Notes
  notes: string;
}

const STEPS = [
  { id: 'basic', title: 'Basic Info', icon: BookOpen },
  { id: 'config', title: 'Test Config', icon: Settings },
  { id: 'subjects', title: 'Subjects', icon: Target },
  { id: 'goals', title: 'Goals', icon: Target },
  { id: 'analysis', title: 'Analysis', icon: AlertCircle },
  { id: 'review', title: 'Review', icon: Check }
];

export function EnhancedAddMockTestDialog({
  open,
  onOpenChange,
  onTestAdded,
  initialData
}: EnhancedAddMockTestDialogProps) {
  const { user } = useSupabaseAuth();
  const { subjects } = useSupabaseSubjectStore();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState<TestCategory[]>([]);

  // Form state
  const [formData, setFormData] = useState<FormData>({
    name: '',
    date: new Date(),
    time: '09:00',
    categoryId: '',
    testType: 'mock',
    testEnvironment: 'home',
    duration: '',
    totalQuestions: '',
    testPaperUrl: '',
    targetScore: '',
    confidenceLevel: 3,
    preparationTime: '',
    studyMaterials: [],
    expectedDifficulty: 'medium',
    subjectInputs: [{ subjectId: '', marksObtained: '', totalMarks: '', timeSpent: '', questionsAttempted: '' }],
    enableMistakeTracking: true,
    enableTakeawayCollection: true,
    reviewReminderEnabled: true,
    syllabus: [],
    initialMistakes: [''],
    initialTakeaways: [''],
    notes: ''
  });

  // Load categories
  useEffect(() => {
    if (user?.id) {
      const userCategories = categoryStorage.getAll(user.id);
      setCategories(userCategories);
    }
  }, [user?.id]);

  // Initialize form with initial data
  useEffect(() => {
    if (initialData && open) {
      setFormData(prev => ({
        ...prev,
        name: initialData.name || '',
        date: initialData.date ? new Date(initialData.date) : new Date(),
        time: initialData.time || '09:00',
        categoryId: initialData.categoryId || '',
        testType: initialData.testType || 'mock',
        testEnvironment: initialData.testEnvironment || 'home',
        duration: initialData.duration?.toString() || '',
        totalQuestions: initialData.totalQuestions?.toString() || '',
        testPaperUrl: initialData.testPaperUrl || '',
        targetScore: initialData.targetScore?.toString() || '',
        confidenceLevel: initialData.confidenceLevel || 3,
        preparationTime: initialData.preparationTime?.toString() || '',
        expectedDifficulty: initialData.difficulty || 'medium',
        notes: initialData.notes || ''
      }));
    }
  }, [initialData, open]);

  const updateFormData = (updates: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const addSubjectInput = () => {
    setFormData(prev => ({
      ...prev,
      subjectInputs: [...prev.subjectInputs, { 
        subjectId: '', 
        marksObtained: '', 
        totalMarks: '', 
        timeSpent: '', 
        questionsAttempted: '' 
      }]
    }));
  };

  const removeSubjectInput = (index: number) => {
    setFormData(prev => ({
      ...prev,
      subjectInputs: prev.subjectInputs.filter((_, i) => i !== index)
    }));
  };

  const updateSubjectInput = (index: number, updates: Partial<SubjectMarkInput>) => {
    setFormData(prev => ({
      ...prev,
      subjectInputs: prev.subjectInputs.map((input, i) => 
        i === index ? { ...input, ...updates } : input
      )
    }));
  };

  const validateCurrentStep = (): boolean => {
    const step = STEPS[currentStep];

    switch (step.id) {
      case 'basic':
        return !!(formData.name && formData.date);
      case 'config':
        return true; // All fields are optional
      case 'subjects':
        return formData.subjectInputs.some(input =>
          input.subjectId && input.marksObtained && input.totalMarks
        );
      case 'goals':
        return !!formData.targetScore;
      case 'analysis':
        return true; // All fields are optional
      case 'review':
        return true;
      default:
        return true;
    }
  };

  const nextStep = () => {
    if (validateCurrentStep() && currentStep < STEPS.length - 1) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    if (!user?.id) return;

    setIsSubmitting(true);
    try {
      // Process subject inputs
      const subjectMarks: SubjectMarks[] = formData.subjectInputs
        .filter(input => input.subjectId && input.marksObtained && input.totalMarks)
        .map(input => {
          const subject = subjects.find(s => s.id === input.subjectId);
          if (!subject) throw new Error("Subject not found");
          
          return {
            subject: subject.name,
            subjectColor: subject.color,
            marksObtained: parseFloat(input.marksObtained),
            totalMarks: parseFloat(input.totalMarks)
          };
        });

      if (subjectMarks.length === 0) {
        throw new Error("At least one subject with marks is required");
      }

      // Calculate totals
      const { totalMarksObtained, totalMarks } = calculateTestTotals(subjectMarks);

      // Process initial mistakes and takeaways
      const initialMistakes = formData.initialMistakes
        .filter(mistake => mistake.trim() !== '')
        .map(description => ({
          id: `mistake-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          category: 'other' as const,
          subject: '',
          topic: '',
          description,
          severity: 'medium' as const,
          createdAt: new Date().toISOString(),
          resolved: false
        }));

      const initialTakeaways = formData.initialTakeaways
        .filter(takeaway => takeaway.trim() !== '')
        .map(description => ({
          id: `takeaway-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          category: 'other' as const,
          description,
          priority: 'medium' as const,
          implemented: false,
          createdAt: new Date().toISOString()
        }));

      const mockTest: MockTest = {
        id: `mock-${Date.now()}`,
        name: formData.name,
        date: format(formData.date, "yyyy-MM-dd"),
        time: formData.time,
        subjectMarks,
        totalMarksObtained,
        totalMarks,
        notes: formData.notes,
        createdAt: new Date().toISOString(),
        userId: user.id,

        // Enhanced fields
        testType: formData.testType,
        testEnvironment: formData.testEnvironment,
        duration: formData.duration ? parseInt(formData.duration) : undefined,
        totalQuestions: formData.totalQuestions ? parseInt(formData.totalQuestions) : undefined,
        categoryId: formData.categoryId || undefined,
        testPaperUrl: formData.testPaperUrl || undefined,

        targetScore: parseInt(formData.targetScore),
        confidenceLevel: formData.confidenceLevel,
        preparationTime: formData.preparationTime ? parseFloat(formData.preparationTime) : undefined,
        studyMaterials: formData.studyMaterials,

        // Syllabus
        syllabus: {
          topics: formData.syllabus.filter(topic => topic.trim() !== ''),
          chapters: [],
          overallProgress: 0,
          lastUpdated: new Date().toISOString()
        },

        difficulty: formData.expectedDifficulty,
        isReviewed: false,
        mistakes: initialMistakes,
        takeaways: initialTakeaways,
        analysisCompleted: initialMistakes.length > 0 || initialTakeaways.length > 0,
        mistakesAnalyzed: initialMistakes.length > 0,
        takeawaysRecorded: initialTakeaways.length > 0,

        status: 'completed',
        completedAt: new Date().toISOString(),
        resultEnteredAt: new Date().toISOString()
      };

      await saveMockTest(mockTest);

      // Reset form
      resetForm();
      onOpenChange(false);

      toast({
        title: "Success",
        description: "Mock test added successfully with comprehensive details",
      });
      
      if (onTestAdded) {
        onTestAdded();
      }
    } catch (error) {
      console.error("Error saving mock test:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save mock test",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      date: new Date(),
      time: '09:00',
      categoryId: '',
      testType: 'mock',
      testEnvironment: 'home',
      duration: '',
      totalQuestions: '',
      testPaperUrl: '',
      targetScore: '',
      confidenceLevel: 3,
      preparationTime: '',
      studyMaterials: [],
      expectedDifficulty: 'medium',
      subjectInputs: [{ subjectId: '', marksObtained: '', totalMarks: '', timeSpent: '', questionsAttempted: '' }],
      enableMistakeTracking: true,
      enableTakeawayCollection: true,
      reviewReminderEnabled: true,
      syllabus: [],
      initialMistakes: [''],
      initialTakeaways: [''],
      notes: ''
    });
    setCurrentStep(0);
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-full sm:max-w-[600px] lg:max-w-[800px] max-h-[90vh] flex flex-col">
        <DialogHeader className="pb-2">
          <DialogTitle className="flex items-center gap-2 text-base lg:text-lg">
            <Plus className="h-4 w-4 text-violet-600" />
            Add Mock Test
          </DialogTitle>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="mb-4">
          {/* Desktop Step Indicator */}
          <div className="hidden md:flex items-center justify-between">
            {STEPS.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;

              return (
                <div key={step.id} className="flex items-center">
                  <div className={cn(
                    "flex items-center justify-center w-7 h-7 rounded-full border transition-all",
                    isActive && "border-violet-600 bg-violet-600 text-white",
                    isCompleted && "border-emerald-600 bg-emerald-600 text-white",
                    !isActive && !isCompleted && "border-gray-300 text-gray-400 dark:border-gray-600 dark:text-gray-500"
                  )}>
                    {isCompleted ? (
                      <Check className="h-3.5 w-3.5" />
                    ) : (
                      <Icon className="h-3.5 w-3.5" />
                    )}
                  </div>
                  <span className={cn(
                    "ml-1.5 text-xs font-medium",
                    isActive && "text-violet-600 dark:text-violet-400",
                    isCompleted && "text-emerald-600 dark:text-emerald-400",
                    !isActive && !isCompleted && "text-gray-400 dark:text-gray-500"
                  )}>
                    {step.title}
                  </span>
                  {index < STEPS.length - 1 && (
                    <div className={cn(
                      "w-4 h-0.5 mx-2",
                      index < currentStep ? "bg-emerald-600" : "bg-gray-300 dark:bg-gray-600"
                    )} />
                  )}
                </div>
              );
            })}
          </div>

          {/* Mobile Step Indicator */}
          <div className="md:hidden">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-muted-foreground">
                Step {currentStep + 1} of {STEPS.length}
              </span>
              <span className="text-xs font-medium text-violet-600 dark:text-violet-400">
                {STEPS[currentStep].title}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
              <div
                className="bg-violet-600 h-1.5 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / STEPS.length) * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Step Content */}
        <div className="flex-1 min-h-0 overflow-y-auto px-1 py-4">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {STEPS[currentStep].id === 'basic' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Basic Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        <div className="space-y-1.5">
                          <Label htmlFor="testName" className="text-xs">Test Name *</Label>
                          <Input
                            id="testName"
                            placeholder="Enter test name"
                            value={formData.name}
                            onChange={(e) => updateFormData({ name: e.target.value })}
                            className="h-8 text-sm"
                          />
                        </div>

                        <div className="space-y-1.5">
                          <Label className="text-xs">Test Type</Label>
                          <Select value={formData.testType} onValueChange={(value: TestType) => updateFormData({ testType: value })}>
                            <SelectTrigger className="h-8 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="practice">Practice Test</SelectItem>
                              <SelectItem value="mock">Mock Test</SelectItem>
                              <SelectItem value="actual_exam">Actual Exam</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        <div className="space-y-1.5">
                          <Label className="text-xs">Test Date *</Label>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal h-8 text-sm",
                                  !formData.date && "text-muted-foreground"
                                )}
                              >
                                <CalendarIcon className="mr-2 h-3.5 w-3.5" />
                                {formData.date ? format(formData.date, "MMM dd, yyyy") : "Pick a date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={formData.date}
                                onSelect={(date) => date && updateFormData({ date })}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>

                        <div className="space-y-1.5">
                          <Label htmlFor="testTime" className="text-xs">Test Time</Label>
                          <Input
                            id="testTime"
                            type="time"
                            value={formData.time}
                            onChange={(e) => updateFormData({ time: e.target.value })}
                            className="h-8 text-sm"
                          />
                        </div>
                      </div>

                      <div className="space-y-1.5">
                        <Label className="text-xs">Category</Label>
                        <Select value={formData.categoryId} onValueChange={(value) => updateFormData({ categoryId: value })}>
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select a category (optional)" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {STEPS[currentStep].id === 'config' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Test Configuration</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        <div className="space-y-1.5">
                          <Label className="text-xs">Test Environment</Label>
                          <Select value={formData.testEnvironment} onValueChange={(value: TestEnvironment) => updateFormData({ testEnvironment: value })}>
                            <SelectTrigger className="h-8 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="home">Home</SelectItem>
                              <SelectItem value="center">Test Center</SelectItem>
                              <SelectItem value="online">Online</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-1.5">
                          <Label htmlFor="duration" className="text-xs">Duration (minutes)</Label>
                          <Input
                            id="duration"
                            type="number"
                            placeholder="e.g., 180"
                            value={formData.duration}
                            onChange={(e) => updateFormData({ duration: e.target.value })}
                            className="h-8 text-sm"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                        <div className="space-y-1.5">
                          <Label htmlFor="totalQuestions" className="text-xs">Total Questions</Label>
                          <Input
                            id="totalQuestions"
                            type="number"
                            placeholder="e.g., 100"
                            value={formData.totalQuestions}
                            onChange={(e) => updateFormData({ totalQuestions: e.target.value })}
                            className="h-8 text-sm"
                          />
                        </div>

                        <div className="space-y-1.5">
                          <Label className="text-xs">Expected Difficulty</Label>
                          <Select value={formData.expectedDifficulty} onValueChange={(value: DifficultyLevel) => updateFormData({ expectedDifficulty: value })}>
                            <SelectTrigger className="h-8 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="easy">Easy</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="hard">Hard</SelectItem>
                              <SelectItem value="very_hard">Very Hard</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-1.5">
                        <Label htmlFor="testPaperUrl" className="text-xs">Test Paper URL</Label>
                        <Input
                          id="testPaperUrl"
                          placeholder="https://example.com/test-paper.pdf"
                          value={formData.testPaperUrl}
                          onChange={(e) => updateFormData({ testPaperUrl: e.target.value })}
                          className="h-8 text-sm"
                        />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {STEPS[currentStep].id === 'subjects' && (
                <div className="space-y-3">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base">Subject-wise Performance</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {formData.subjectInputs.map((input, index) => (
                        <div key={index} className="p-3 border rounded-lg space-y-2 bg-muted/10">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium">Subject {index + 1}</h4>
                            {formData.subjectInputs.length > 1 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeSubjectInput(index)}
                                className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                              >
                                <Trash2 className="h-3.5 w-3.5" />
                              </Button>
                            )}
                          </div>

                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
                            <div className="space-y-1.5">
                              <Label className="text-xs">Subject</Label>
                              <Select
                                value={input.subjectId}
                                onValueChange={(value) => updateSubjectInput(index, { subjectId: value })}
                              >
                                <SelectTrigger className="h-8 text-sm">
                                  <SelectValue placeholder="Select subject" />
                                </SelectTrigger>
                                <SelectContent>
                                  {subjects.map((subject) => (
                                    <SelectItem key={subject.id} value={subject.id}>
                                      <div className="flex items-center gap-2">
                                        <div
                                          className="w-2.5 h-2.5 rounded-full"
                                          style={{ backgroundColor: subject.color }}
                                        />
                                        {subject.name}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1.5">
                              <Label className="text-xs">Time Spent (minutes)</Label>
                              <Input
                                type="number"
                                placeholder="e.g., 45"
                                value={input.timeSpent}
                                onChange={(e) => updateSubjectInput(index, { timeSpent: e.target.value })}
                                className="h-8 text-sm"
                              />
                            </div>
                          </div>

                          <div className="grid grid-cols-2 lg:grid-cols-3 gap-2">
                            <div className="space-y-1.5">
                              <Label className="text-xs">Marks Obtained *</Label>
                              <Input
                                type="number"
                                placeholder="e.g., 85"
                                value={input.marksObtained}
                                onChange={(e) => updateSubjectInput(index, { marksObtained: e.target.value })}
                                className="h-8 text-sm"
                              />
                            </div>

                            <div className="space-y-1.5">
                              <Label className="text-xs">Total Marks *</Label>
                              <Input
                                type="number"
                                placeholder="e.g., 100"
                                value={input.totalMarks}
                                onChange={(e) => updateSubjectInput(index, { totalMarks: e.target.value })}
                                className="h-8 text-sm"
                              />
                            </div>

                            <div className="space-y-1.5">
                              <Label className="text-xs">Questions Attempted</Label>
                              <Input
                                type="number"
                                placeholder="e.g., 25"
                                value={input.questionsAttempted}
                                onChange={(e) => updateSubjectInput(index, { questionsAttempted: e.target.value })}
                                className="h-8 text-sm"
                              />
                            </div>
                          </div>

                          {input.marksObtained && input.totalMarks && (
                            <div className="mt-1 p-1.5 bg-muted/30 rounded text-xs">
                              <span className="font-medium text-gray-700 dark:text-gray-300">Percentage: </span>
                              <span className="text-emerald-600 dark:text-emerald-400 font-semibold">
                                {((parseFloat(input.marksObtained) / parseFloat(input.totalMarks)) * 100).toFixed(1)}%
                              </span>
                            </div>
                          )}
                        </div>
                      ))}

                      <Button
                        variant="outline"
                        onClick={addSubjectInput}
                        className="w-full border-dashed h-8 text-xs"
                      >
                        <Plus className="h-3.5 w-3.5 mr-1.5" />
                        Add Another Subject
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              )}

              {STEPS[currentStep].id === 'goals' && (
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Goals & Preparation</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="targetScore">Target Score *</Label>
                          <Input
                            id="targetScore"
                            type="number"
                            placeholder="e.g., 450"
                            value={formData.targetScore}
                            onChange={(e) => updateFormData({ targetScore: e.target.value })}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="preparationTime">Preparation Time (hours)</Label>
                          <Input
                            id="preparationTime"
                            type="number"
                            step="0.5"
                            placeholder="e.g., 25.5"
                            value={formData.preparationTime}
                            onChange={(e) => updateFormData({ preparationTime: e.target.value })}
                          />
                        </div>
                      </div>

                      <div className="space-y-3">
                        <Label>Confidence Level: {formData.confidenceLevel}/5</Label>
                        <Slider
                          value={[formData.confidenceLevel]}
                          onValueChange={([value]) => updateFormData({ confidenceLevel: value as ConfidenceLevel })}
                          max={5}
                          min={1}
                          step={1}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Very Low</span>
                          <span>Low</span>
                          <span>Medium</span>
                          <span>High</span>
                          <span>Very High</span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="notes">Notes & Observations</Label>
                        <Textarea
                          id="notes"
                          placeholder="Add any notes about this test, preparation strategy, or expectations..."
                          value={formData.notes}
                          onChange={(e) => updateFormData({ notes: e.target.value })}
                          className="min-h-[100px]"
                        />
                      </div>

                      <div className="space-y-3">
                        <Label>Analysis Preferences</Label>
                        <div className="space-y-2">
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={formData.enableMistakeTracking}
                              onChange={(e) => updateFormData({ enableMistakeTracking: e.target.checked })}
                              className="rounded"
                            />
                            <span className="text-sm">Enable mistake tracking for this test</span>
                          </label>

                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={formData.enableTakeawayCollection}
                              onChange={(e) => updateFormData({ enableTakeawayCollection: e.target.checked })}
                              className="rounded"
                            />
                            <span className="text-sm">Enable takeaway collection</span>
                          </label>

                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={formData.reviewReminderEnabled}
                              onChange={(e) => updateFormData({ reviewReminderEnabled: e.target.checked })}
                              className="rounded"
                            />
                            <span className="text-sm">Set review reminders</span>
                          </label>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {STEPS[currentStep].id === 'analysis' && (
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <AlertCircle className="h-5 w-5 text-violet-600" />
                        Test Analysis Setup
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <Label>Syllabus / Topics Covered</Label>
                        <div className="space-y-2">
                          {formData.syllabus.map((topic, index) => (
                            <div key={index} className="flex gap-2">
                              <Input
                                placeholder="Enter a topic or chapter covered in this test..."
                                value={topic}
                                onChange={(e) => {
                                  const newSyllabus = [...formData.syllabus];
                                  newSyllabus[index] = e.target.value;
                                  updateFormData({ syllabus: newSyllabus });
                                }}
                              />
                              {formData.syllabus.length > 1 && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    const newSyllabus = formData.syllabus.filter((_, i) => i !== index);
                                    updateFormData({ syllabus: newSyllabus });
                                  }}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          ))}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateFormData({ syllabus: [...formData.syllabus, ''] })}
                            className="w-full border-dashed"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Topic
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-3">
                          <Label className="flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-500" />
                            Initial Mistakes
                          </Label>
                          <div className="space-y-2">
                            {formData.initialMistakes.map((mistake, index) => (
                              <div key={index} className="flex gap-2">
                                <Input
                                  placeholder="Describe a mistake or area of confusion..."
                                  value={mistake}
                                  onChange={(e) => {
                                    const newMistakes = [...formData.initialMistakes];
                                    newMistakes[index] = e.target.value;
                                    updateFormData({ initialMistakes: newMistakes });
                                  }}
                                />
                                {formData.initialMistakes.length > 1 && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const newMistakes = formData.initialMistakes.filter((_, i) => i !== index);
                                      updateFormData({ initialMistakes: newMistakes });
                                    }}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            ))}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateFormData({ initialMistakes: [...formData.initialMistakes, ''] })}
                              className="w-full border-dashed"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add Mistake
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <Label className="flex items-center gap-2">
                            <Check className="h-4 w-4 text-emerald-500" />
                            Initial Takeaways
                          </Label>
                          <div className="space-y-2">
                            {formData.initialTakeaways.map((takeaway, index) => (
                              <div key={index} className="flex gap-2">
                                <Input
                                  placeholder="What did you learn or do well..."
                                  value={takeaway}
                                  onChange={(e) => {
                                    const newTakeaways = [...formData.initialTakeaways];
                                    newTakeaways[index] = e.target.value;
                                    updateFormData({ initialTakeaways: newTakeaways });
                                  }}
                                />
                                {formData.initialTakeaways.length > 1 && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      const newTakeaways = formData.initialTakeaways.filter((_, i) => i !== index);
                                      updateFormData({ initialTakeaways: newTakeaways });
                                    }}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            ))}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateFormData({ initialTakeaways: [...formData.initialTakeaways, ''] })}
                              className="w-full border-dashed"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add Takeaway
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {STEPS[currentStep].id === 'review' && (
                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Review & Confirm</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <h4 className="font-medium text-violet-700 dark:text-violet-400">Test Details</h4>
                          <div className="space-y-1 text-muted-foreground">
                            <p><span className="font-medium">Name:</span> {formData.name}</p>
                            <p><span className="font-medium">Date:</span> {format(formData.date, 'PPP')}</p>
                            <p><span className="font-medium">Time:</span> {formData.time}</p>
                            <p><span className="font-medium">Type:</span> {formData.testType}</p>
                            <p><span className="font-medium">Environment:</span> {formData.testEnvironment}</p>
                            {formData.duration && <p><span className="font-medium">Duration:</span> {formData.duration} minutes</p>}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <h4 className="font-medium text-violet-700 dark:text-violet-400">Performance Goals</h4>
                          <div className="space-y-1 text-muted-foreground">
                            <p><span className="font-medium">Target Score:</span> {formData.targetScore}</p>
                            <p><span className="font-medium">Confidence:</span> {formData.confidenceLevel}/5</p>
                            {formData.preparationTime && <p><span className="font-medium">Prep Time:</span> {formData.preparationTime} hours</p>}
                            <p><span className="font-medium">Expected Difficulty:</span> {formData.expectedDifficulty}</p>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-violet-700 dark:text-violet-400">Subjects & Marks</h4>
                        <div className="space-y-2">
                          {formData.subjectInputs
                            .filter(input => input.subjectId && input.marksObtained && input.totalMarks)
                            .map((input, index) => {
                              const subject = subjects.find(s => s.id === input.subjectId);
                              const percentage = ((parseFloat(input.marksObtained) / parseFloat(input.totalMarks)) * 100).toFixed(1);

                              return (
                                <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                                  <div className="flex items-center gap-2">
                                    <div
                                      className="w-3 h-3 rounded-full"
                                      style={{ backgroundColor: subject?.color }}
                                    />
                                    <span className="font-medium">{subject?.name}</span>
                                  </div>
                                  <div className="text-sm text-muted-foreground">
                                    {input.marksObtained}/{input.totalMarks} ({percentage}%)
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      </div>

                      {formData.notes && (
                        <div className="space-y-2">
                          <h4 className="font-medium text-violet-700 dark:text-violet-400">Notes</h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                            {formData.notes}
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>

        <DialogFooter className="flex justify-between pt-3 border-t border-border">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 0}
            className="flex items-center gap-1.5 h-8 text-xs"
            size="sm"
          >
            <ChevronLeft className="h-3.5 w-3.5" />
            <span className="hidden sm:inline">Previous</span>
            <span className="sm:hidden">Prev</span>
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleClose}
              className="h-8 text-xs"
              size="sm"
            >
              Cancel
            </Button>

            {currentStep === STEPS.length - 1 ? (
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || !validateCurrentStep()}
                className="bg-violet-600 hover:bg-violet-700 h-8 text-xs"
                size="sm"
              >
                {isSubmitting ? "Saving..." : "Save Test"}
              </Button>
            ) : (
              <Button
                onClick={nextStep}
                disabled={!validateCurrentStep()}
                className="flex items-center gap-1.5 bg-violet-600 hover:bg-violet-700 h-8 text-xs"
                size="sm"
              >
                Next
                <ChevronRight className="h-3.5 w-3.5" />
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
